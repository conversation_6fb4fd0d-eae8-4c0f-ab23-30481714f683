{"version": 3, "file": "interactions.d.ts", "sourceRoot": "", "sources": ["interactions.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,EACX,qBAAqB,EACrB,+BAA+B,EAC/B,qCAAqC,EACrC,sBAAsB,EACtB,kCAAkC,EAClC,sBAAsB,EACtB,uBAAuB,EACvB,UAAU,EACV,eAAe,EACf,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,EACX,qDAAqD,EACrD,kBAAkB,EAClB,cAAc,EACd,MAAM,uBAAuB,CAAC;AAC/B,OAAO,KAAK,EACX,0CAA0C,EAC1C,uCAAuC,EACvC,+CAA+C,EAC/C,2CAA2C,EAC3C,yCAAyC,EACzC,qCAAqC,EACrC,MAAM,WAAW,CAAC;AAEnB;;GAEG;AACH,MAAM,WAAW,kCAAkC;IAClD;;;;;OAKG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG,qBAAqB,EAAE,CAAC;AAE1E;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,qBAAqB,CAAC;AAEvE,MAAM,WAAW,0CAChB,SAAQ,qDAAqD,CAC3D,IAAI,CACH,qBAAqB,EACnB,gBAAgB,GAChB,UAAU,GACV,4BAA4B,GAC5B,uBAAuB,GACvB,aAAa,GACb,UAAU,GACV,IAAI,GACJ,mBAAmB,GACnB,gBAAgB,GAChB,MAAM,GACN,SAAS,CACX,CACD,EACD,qDAAqD,CACpD,OAAO,CACN,kBAAkB,CAAC,IAAI,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC,GAC1D,IAAI,CAAC,qBAAqB,EAAE,4BAA4B,GAAG,mBAAmB,CAAC,CAChF,CACD;CAAG;AAEN;;GAEG;AACH,MAAM,WAAW,+CAAgD,SAAQ,0CAA0C;IAClH,IAAI,CAAC,EAAE,sBAAsB,CAAC,SAAS,GAAG,SAAS,CAAC;IACpD,WAAW,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,iDAAkD,SAAQ,0CAA0C;IACpH,IAAI,EAAE,sBAAsB,CAAC,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC;CACnE;AAED;;GAEG;AACH,MAAM,WAAW,sDAChB,SAAQ,0CAA0C;IAClD,IAAI,EAAE,sBAAsB,CAAC,iBAAiB,CAAC;CAC/C;AAED;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAC/C,+CAA+C,GAC/C,iDAAiD,GACjD,sDAAsD,CAAC;AAE1D;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,qBAAqB,CAAC;AAEzE;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,cAAc,CAAC,sCAAsC,CAAC,CAAC;AAE5G;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,qBAAqB,CAAC;AAEzE;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAAG,sCAAsC,EAAE,CAAC;AAE7F;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG,qBAAqB,EAAE,CAAC;AAE1E;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,kCAAkC,CAAC;AAEzF;;GAEG;AACH,MAAM,MAAM,wCAAwC,GAAG,IAAI,CAAC,qBAAqB,EAAE,eAAe,CAAC,EAAE,CAAC;AAEtG;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,IAAI,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;AAEnG;;GAEG;AACH,MAAM,MAAM,2CAA2C,GACpD,IAAI,CAAC,+CAA+C,EAAE,eAAe,CAAC,GACtE,IAAI,CAAC,iDAAiD,EAAE,eAAe,CAAC,CAAC;AAE5E;;GAEG;AACH,MAAM,MAAM,yCAAyC,GAAG,IAAI,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;AAErG;;GAEG;AACH,MAAM,MAAM,2CAA2C,GAAG,cAAc,CACrE,IAAI,CAAC,+CAA+C,EAAE,eAAe,CAAC,GACtE,IAAI,CAAC,iDAAiD,EAAE,eAAe,CAAC,CAC1E,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,yCAAyC,GAAG,IAAI,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;AAErG;;GAEG;AACH,MAAM,MAAM,0CAA0C,GAAG,CACtD,CAAC,IAAI,CAAC,+CAA+C,EAAE,eAAe,CAAC,GACvE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,CAAC,GAC3C,CAAC,IAAI,CAAC,iDAAiD,EAAE,eAAe,CAAC,GACzE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,CAAC,CAC7C,EAAE,CAAC;AAEJ;;GAEG;AACH,MAAM,MAAM,wCAAwC,GAAG,IAAI,CAAC,qBAAqB,EAAE,eAAe,CAAC,EAAE,CAAC;AAEtG;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,sBAAsB,CAAC;AAE5E;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD;;OAEG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,MAAM,0CAA0C,GACnD,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,sCAAsC,CAAC,CAAC;AAElF;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;GAEG;AACH,MAAM,WAAW,gDAAgD;IAChE;;OAEG;IACH,WAAW,EAAE,gCAAgC,CAAC;IAC9C;;OAEG;IACH,QAAQ,CAAC,EAAE,wCAAwC,CAAC;CACpD;AAED;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAChD;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,IAAI,EAAE,eAAe,CAAC;IACtB;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B;;OAEG;IACH,mBAAmB,CAAC,EAAE,SAAS,CAAC;IAChC;;OAEG;IACH,wBAAwB,CAAC,EAAE,OAAO,CAAC;IACnC;;OAEG;IACH,0BAA0B,CAAC,EAAE,OAAO,CAAC;CACrC;AAED;;GAEG;AACH,MAAM,WAAW,wCAAwC;IACxD;;OAEG;IACH,IAAI,EAAE,uBAAuB,CAAC;IAC9B;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,kDAAkD,CAAC;IACvE;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,UAAU,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,kDAAkD;IAClE;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,MAAM,2CAA2C,GAAG,uCAAuC,CAAC;AAElG;;GAEG;AACH,MAAM,MAAM,+CAA+C,GAAG,2CAA2C,CAAC;AAE1G;;GAEG;AACH,MAAM,MAAM,mDAAmD,GAAG,+CAA+C,CAAC;AAElH;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,yCAAyC,CAAC;AAEtG;;GAEG;AACH,MAAM,MAAM,8CAA8C,GAAG,0CAA0C,CAAC;AAExG;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,kCAAkC,CAAC;AAExF;;GAEG;AACH,MAAM,MAAM,0CAA0C,GACnD,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,sCAAsC,CAAC,CAAC;AAElF;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,qCAAqC,CAAC;AAEzF;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG,uCAAuC,CAAC;AAE1F;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,2CAA2C,CAAC;AAElG;;GAEG;AACH,MAAM,MAAM,2CAA2C,GAAG,+CAA+C,CAAC;AAE1G;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAAG,yCAAyC,CAAC;AAE9F;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,0CAA0C,CAAC;AAEhG;;GAEG;AACH,MAAM,MAAM,mDAAmD,GAAG,qCAAqC,EAAE,CAAC;AAE1G;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,qCAAqC,CAAC;AAElG;;GAEG;AACH,MAAM,WAAW,+CAA+C;IAC/D,WAAW,EAAE,+BAA+B,EAAE,CAAC;CAC/C;AAED;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,qCAAqC,CAAC;AAElG;;GAEG;AACH,MAAM,MAAM,qDAAqD,GAAG,IAAI,CACvE,qCAAqC,EACrC,IAAI,GAAG,aAAa,CACpB,EAAE,CAAC;AAEJ;;GAEG;AACH,MAAM,MAAM,mDAAmD,GAAG,qCAAqC,EAAE,CAAC"}