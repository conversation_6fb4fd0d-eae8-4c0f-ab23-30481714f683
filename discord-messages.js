const { Client, GatewayIntentBits } = require('discord.js');
const express = require('express');
const { marked } = require('marked');
require('dotenv').config();

// Configuration
const CONFIG = {
  token: process.env.DISCORD_BOT_TOKEN,
  channels: {
    tab1: {
      id: process.env.DISCORD_CHANNEL_1 || 'YOUR_CHANNEL_1_ID_HERE',
      name: process.env.DISCORD_CHANNEL_1_NAME || 'Channel 1'
    },
    tab2: {
      id: process.env.DISCORD_CHANNEL_2 || 'YOUR_CHANNEL_2_ID_HERE',
      name: process.env.DISCORD_CHANNEL_2_NAME || 'Channel 2'
    },
    tab3: {
      id: process.env.DISCORD_CHANNEL_3 || 'YOUR_CHANNEL_3_ID_HERE',
      name: process.env.DISCORD_CHANNEL_3_NAME || 'Channel 3'
    }
  },
  messageLimit: 5, // Number of latest messages to fetch
  port: process.env.PORT || 3000
};

// Create Express app
const app = express();

// Create Discord client with necessary intents
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent
  ]
});

// Function to format messages as HTML with tabs
function formatTabbedHTML(channelsData) {
  let html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Discord Messages</title>
      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">

      <style>
        body {
          font-family: "Space-Mono", sans-serif;
          margin: 0 auto;
          padding: 20px;
          color: #fff;
          line-height: 1.6;
        }
        .tabs {
          display: flex;
          margin-bottom: 30px;
          gap: 30px;
        }
        .tab {
          padding: 10px 20px;
          cursor: pointer;
          border: none;
          background: #f5f5f5;
          margin-right: 5px;
          border-radius: 5px 5px 0 0;
          gab
        }
        .tab.active {
          background: #fff;
          color: #000;
        }
        .tab:hover {
          background: #e0e0e0;
        }
        .tab.active:hover {
          background: #fff;
          color: #000;
        }
        .tab-content {
          display: none;
        }
        .tab-content.active {
          display: block;
        }
        .message {
          margin-bottom: 20px;
          padding-bottom: 15px;
        }
        .message p {
          color: #fff !important;
        }
        .timestamp {
          font-size: 0.9em;
          color: #fff;
          margin-bottom: 10px;
        }
        .content {
          margin-bottom: 10px;
        }
        .attachment {
          background: #f5f5f5;
          padding: 10px;
          margin: 10px 0;
        }
        a {
          color: #fff;
          border-bottom: 1px solid #fff;
        }
        h1, h2, h3, h4, h5, h6 {
          margin: 10px 0;
        }
        code {
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 0.9em;
        }
        pre {
          padding: 10px;
          border-radius: 5px;
          overflow-x: auto;
          font-size: 0.9em;
        }
        blockquote {
          border-left: 4px solid #ccc;
          margin: 10px 0;
          padding-left: 15px;
          color: #666;
        }


        @media(max-width:680px) {
          .tabs {
              display: block;
          }
          button.tab {
              width: 100%;
              display: block;
              margin-bottom: 30px;
          }
      }


.glass-button {
  position: relative;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px !important;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none !important;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden !important;
  display: inline-block;
  min-height: 10px;
  width: 100%;
    box-sizing: border-box;
}

.glass-button::before {
  opacity: 0;
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}


.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

      </style>

     
    </head>
    <body>

      <div class="tabs">
  `;

  // Generate tab buttons
  Object.keys(channelsData).forEach((tabKey, index) => {
    const isActive = index === 0 ? 'active' : '';
    html += `<button class="tab ${isActive} glass-button" onclick="showTab('${tabKey}')">${channelsData[tabKey].channelInfo.name}</button>`;
  });

  html += `</div>`;

  // Generate tab content
  Object.keys(channelsData).forEach((tabKey, index) => {
    const isActive = index === 0 ? 'active' : '';
    const data = channelsData[tabKey];

    html += `<div id="${tabKey}" class="tab-content ${isActive}">`;

    if (data.error) {
      html += `<p style="color: red;">Error: ${data.error}</p>`;
    } else {
      const messageArray = Array.from(data.messages.values()); // Keep newest first (no reverse)

      messageArray.forEach((message) => {
        const timestamp = message.createdAt.toLocaleString();
        let content = message.content || '[No text content]';

        // Strip out Discord pings/mentions and custom emojis
        if (content && content !== '[No text content]') {
          // Replace specific role mention with "Bedrock": <@&798261674506190899>
          content = content.replace(/<@&798261674506190899>/g, 'Bedrock');

          // Remove all other role mentions: <@&123456789>
          content = content.replace(/<@&\d+>/g, '');

          // Remove user mentions: <@123456789> or <@!123456789>
          content = content.replace(/<@!?\d+>/g, '');

          // Remove channel mentions: <#123456789>
          content = content.replace(/<#\d+>/g, '');

          // Remove custom Discord emojis: <:name:123456789> or <a:name:123456789>
          content = content.replace(/<a?:[^:]+:\d+>/g, '');

          // Remove everyone/here mentions
          content = content.replace(/@everyone/g, '');
          content = content.replace(/@here/g, '');

          // Clean up extra whitespace left by removed mentions (but preserve line breaks)
          content = content.replace(/ +/g, ' ').trim();

          try {
            content = marked(content);
          } catch (err) {
            console.error('Markdown parsing error:', err);
            content = content.replace(/\n/g, '<br>');
          }
        }

        html += `
          <div class="message glass-button">
            <div class="timestamp">${timestamp}</div>
            <div class="content">${content}</div>
        `;

        if (message.embeds.length > 0) {
          html += `<div class="attachment"><strong>Embeds:</strong> ${message.embeds.length}</div>`;
        }

        html += `</div>`;
      });
    }

    html += `</div>`;
  });

  html += `
      <script>
        function showTab(tabId) {
          // Hide all tab contents
          const contents = document.querySelectorAll('.tab-content');
          contents.forEach(content => content.classList.remove('active'));

          // Remove active class from all tabs
          const tabs = document.querySelectorAll('.tab');
          tabs.forEach(tab => tab.classList.remove('active'));

          // Show selected tab content
          document.getElementById(tabId).classList.add('active');

          // Add active class to clicked tab
          event.target.classList.add('active');
        }
      </script>
    </body>
    </html>
  `;

  return html;
}

// Function to fetch latest messages from a specific channel
async function fetchLatestMessages(channelId) {
  try {
    const channel = await client.channels.fetch(channelId);

    if (!channel) {
      throw new Error('Channel not found! Check your channel ID');
    }

    if (!channel.isTextBased()) {
      throw new Error('Channel is not a text channel!');
    }

    // Fetch messages
    const messages = await channel.messages.fetch({
      limit: CONFIG.messageLimit
    });

    return {
      messages,
      channelInfo: {
        name: channel.name,
        guild: channel.guild.name
      }
    };

  } catch (error) {
    console.error('❌ Error fetching messages:', error.message);
    throw error;
  }
}

// Function to fetch messages from all configured channels
async function fetchAllChannelsMessages() {
  const results = {};

  for (const [tabKey, channelConfig] of Object.entries(CONFIG.channels)) {
    try {
      const data = await fetchLatestMessages(channelConfig.id);
      // Override the channel name with the configured name from env
      data.channelInfo.name = channelConfig.name;
      results[tabKey] = data;
    } catch (error) {
      results[tabKey] = {
        error: error.message,
        channelInfo: {
          name: channelConfig.name,
          guild: 'Unknown'
        }
      };
    }
  }

  return results;
}

app.get('/messages', async (req, res) => {
  try {
    if (!client.isReady()) {
      return res.status(503).send(`
        <h1>🔄 Bot is starting up...</h1>
        <p>Please wait a moment and <a href="/messages">try again</a>.</p>
        <p><a href="/">← Back to Home</a></p>
      `);
    }

    const channelsData = await fetchAllChannelsMessages();

    // Check if all channels failed
    const allFailed = Object.values(channelsData).every(data => data.error);
    if (allFailed) {
      return res.send(`
        <h1>📭 No messages found</h1>
        <p>All channels appear to be empty or the bot lacks permissions.</p>
        <p><a href="/messages">🔄 Try Again</a> | <a href="/">🏠 Home</a></p>
      `);
    }

    const html = formatTabbedHTML(channelsData);
    res.send(html);

  } catch (error) {
    console.error('Error in /messages route:', error);

    res.status(500).send(`
      <h1>❌ Error</h1>
      <p>An error occurred while fetching messages.</p>
      <p><a href="/messages">🔄 Try Again</a> | <a href="/">🏠 Home</a></p>
    `);
  }
});

// Bot ready event
client.once('ready', () => {
  console.log(`✅ Bot logged in as ${client.user.tag}`);
  console.log(`🎯 Target Channels:`);
  Object.entries(CONFIG.channels).forEach(([key, channel]) => {
    console.log(`   ${channel.name}: ${channel.id}`);
  });

  // Start web server
  app.listen(CONFIG.port, () => {
    console.log(`🌐 Web server running on http://localhost:${CONFIG.port}`);
    console.log(`📋 View messages at: http://localhost:${CONFIG.port}/messages`);
  });
});

// Error handling
client.on('error', error => {
  console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', error => {
  console.error('❌ Unhandled promise rejection:', error);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  client.destroy();
  process.exit(0);
});

// Validation and startup
if (!CONFIG.token) {
  console.error('❌ DISCORD_BOT_TOKEN not found in environment variables!');
  console.log('💡 Create a .env file with: DISCORD_BOT_TOKEN=your_bot_token_here');
  process.exit(1);
}

// Check if any channels are not configured
const unconfiguredChannels = Object.entries(CONFIG.channels).filter(([key, channel]) =>
  channel.id.includes('YOUR_CHANNEL') || !channel.id
);

if (unconfiguredChannels.length > 0) {
  console.error('❌ Please configure all channel IDs in your .env file!');
  console.log('💡 Add to .env file:');
  console.log('   DISCORD_CHANNEL_1=your_first_channel_id');
  console.log('   DISCORD_CHANNEL_2=your_second_channel_id');
  console.log('   DISCORD_CHANNEL_3=your_third_channel_id');
  console.log('   DISCORD_CHANNEL_1_NAME=Channel 1 Name (optional)');
  console.log('   DISCORD_CHANNEL_2_NAME=Channel 2 Name (optional)');
  console.log('   DISCORD_CHANNEL_3_NAME=Channel 3 Name (optional)');
  process.exit(1);
}

// Login to Discord
console.log('🚀 Starting Discord bot...');
client.login(CONFIG.token);
