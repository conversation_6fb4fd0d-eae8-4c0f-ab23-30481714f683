# Discord Message Reader

A web-based Node.js application that displays the latest Discord messages from multiple channels in a clean, tabbed interface.

## Features

- 📨 **Multi-Channel Support**: Display messages from 3 different Discord channels
- 🗂️ **Tabbed Interface**: Clean tabs to switch between channels instantly
- 📝 **Markdown Rendering**: Full support for Discord markdown formatting
- 🧹 **Smart Content Filtering**: Strips pings, custom emojis, with special handling
- 🎨 **Clean Design**: Minimal white background with simple styling
- ⚡ **Manual Refresh**: Load messages only when you visit/refresh the page
- 🔧 **Configurable**: Custom tab names and channel selection via environment variables

## Content Filtering

The application intelligently processes Discord messages:

### **Markdown Support**
- **Bold**: `**text**` → **text**
- **Italic**: `*text*` → *text*
- **Code**: `` `code` `` → `code`
- **Code Blocks**: ``` blocks render properly
- **Headers**: `# Header` and underlined headers (`===`)
- **Links**: Auto-converted to clickable links
- **Line Breaks**: Preserved for proper formatting

### **Smart Ping/Mention Handling**
- **Special Role**: `<@&798261674506190899>` → "Bedrock"
- **Other Roles**: `<@&123456789>` → *(removed)*
- **User Mentions**: `<@123456789>` → *(removed)*
- **Channel Mentions**: `<#123456789>` → *(removed)*
- **Everyone/Here**: `@everyone`, `@here` → *(removed)*

### **Emoji Filtering**
- **Custom Discord Emojis**: `<:name:123456789>` → *(removed)*
- **Animated Emojis**: `<a:name:123456789>` → *(removed)*
- **Unicode Emojis**: 😀 🎉 → *(preserved)*

## Setup Instructions

### 1. Create a Discord Bot

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name
3. Go to "Bot" section and click "Add Bot"
4. Copy the bot token (keep this secret!)
5. Enable these bot permissions:
   - `Read Messages/View Channels`
   - `Read Message History`

### 2. Invite Bot to Your Server

1. Go to "OAuth2" > "URL Generator"
2. Select scopes: `bot`
3. Select permissions: `Read Messages/View Channels`, `Read Message History`
4. Copy the generated URL and open it to invite the bot

### 3. Get Channel IDs

1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
2. Right-click on each channel you want to monitor
3. Click "Copy ID"
4. You'll need 3 channel IDs for the 3 tabs

### 4. Install and Run

```bash
# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
```

### 5. Configure Environment Variables

Edit your `.env` file:

```bash
# Required: Discord Bot Token
DISCORD_BOT_TOKEN=your_actual_bot_token

# Required: Channel IDs for the 3 tabs
DISCORD_CHANNEL_1=123456789012345678
DISCORD_CHANNEL_2=234567890123456789
DISCORD_CHANNEL_3=345678901234567890

# Optional: Custom tab names (defaults to "Channel 1", "Channel 2", "Channel 3")
DISCORD_CHANNEL_1_NAME=Announcements
DISCORD_CHANNEL_2_NAME=General
DISCORD_CHANNEL_3_NAME=Updates

# Optional: Server port (defaults to 3000)
PORT=3000
```

### 6. Start the Application

```bash
# Run the application
npm start

# Or run with auto-restart during development
npm run dev

# Visit in browser
# http://localhost:3000 - Home page
# http://localhost:3000/messages - View messages
```

## Configuration

- **Message Limit**: Fixed at 5 latest messages per channel
- **Sorting**: Messages display newest to oldest
- **Manual Refresh**: No auto-refresh - reload page to update

## Web Interface

The application provides a clean web interface:

### **Home Page** (`/`)
- Welcome page with instructions
- Navigation to message viewer

### **Messages Page** (`/messages`)
- **Tabbed Interface**: 3 tabs for different channels
- **Clean Design**: White background, minimal styling
- **Message Display**:
  - Newest messages first
  - Timestamps for each message
  - Rendered markdown formatting
  - Filtered content (no pings/custom emojis)

### **Sample Message Display**

```
Tab: [Announcements] [General] [Updates]

12/8/2024, 2:32:10 PM
Here's an important update with **bold text** and *italic text*!

Check out this code:
```javascript
function hello() {
  return "world";
}
```

12/8/2024, 2:31:22 PM
Hey Bedrock! This message had a role ping that got replaced.

12/8/2024, 2:30:15 PM
Simple message with 😀 emoji (unicode emojis are preserved).
```

## Dependencies

```json
{
  "discord.js": "^14.14.1",
  "dotenv": "^16.3.1",
  "express": "^4.18.2",
  "marked": "^9.1.6"
}
```

## Troubleshooting

### **Common Issues**
- **"Channel not found"**: Check your channel IDs and bot permissions
- **"Bot lacks access"**: Ensure bot has proper permissions in all channels
- **"Invalid token"**: Verify your bot token in the .env file
- **Empty tabs**: Check that channels have recent messages and bot can read them
- **Tab names not showing**: Verify `DISCORD_CHANNEL_X_NAME` variables in .env

### **Console Output**
```bash
✅ Bot logged in as YourBot#1234
🎯 Target Channels:
   Announcements: 123456789012345678
   General: 234567890123456789
   Updates: 345678901234567890
🌐 Web server running on http://localhost:3000
📋 View messages at: http://localhost:3000/messages
```

## Security Notes

- Never commit your `.env` file to version control
- Keep your bot token secret
- Only give your bot the minimum required permissions
- The application only reads messages, never sends or modifies them

## Technical Details

- **Framework**: Express.js web server
- **Discord API**: discord.js v14
- **Markdown**: marked.js for rendering
- **Styling**: Vanilla CSS with tabbed interface
- **Message Limit**: 5 messages per channel (configurable in code)
- **Refresh**: Manual only - no auto-refresh or real-time updates
