{"name": "discord-message-reader", "version": "1.0.0", "description": "A simple Node.js app to fetch and display latest Discord messages", "main": "discord-messages.js", "scripts": {"start": "node discord-messages.js", "dev": "nodemon discord-messages.js"}, "dependencies": {"@iframe-resizer/parent": "^5.5.2", "discord.js": "^14.14.1", "dotenv": "^16.3.1", "express": "^4.18.2", "marked": "^9.1.6"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["discord", "bot", "messages", "nodejs"], "author": "Your Name", "license": "MIT"}